import fetch from 'node-fetch';

const GEMINI_API_KEY = 'AIzaSyAZ8CypRH45o3jG6-Kk73s-ftWkzyYU_Ts';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

async function testGeminiAPI() {
  console.log('🧪 Testing Gemini API connectivity...');
  
  try {
    const requestBody = {
      contents: [{
        parts: [{
          text: "Hello! Can you respond with a simple JSON object containing a 'message' field with 'Hello from <PERSON>'?"
        }]
      }],
      generationConfig: {
        temperature: 0.1,
        maxOutputTokens: 100,
        responseMimeType: "application/json"
      }
    };

    console.log('📡 Making test request to Gemini API...');
    
    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    console.log(`📊 Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ HTTP ${response.status}:`, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ Gemini API response received:');
    console.log(JSON.stringify(data, null, 2));
    
    // Try to extract the generated text
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      const generatedText = data.candidates[0].content.parts[0].text;
      console.log('📝 Generated text:', generatedText);
      
      try {
        const parsed = JSON.parse(generatedText);
        console.log('🎉 Successfully parsed JSON:', parsed);
      } catch (e) {
        console.log('⚠️ Could not parse as JSON:', e.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  }
}

testGeminiAPI();
