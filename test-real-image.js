import fetch from 'node-fetch';

const API_KEY = 'NZOo17UfbOhl7BPh9ogJ';

async function testWithRealImage() {
  console.log('🔍 Testing Roboflow with real image...');
  
  try {
    // Use a real image URL that contains objects COCO can detect
    const imageUrl = 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop';
    console.log(`📷 Using image: ${imageUrl}`);
    
    // First, let's get the image and convert to base64
    console.log('📥 Downloading image...');
    const imageResponse = await fetch(imageUrl);
    const imageBuffer = await imageResponse.buffer();
    const base64Image = imageBuffer.toString('base64');
    
    console.log(`📤 Image downloaded (${base64Image.length} chars)`);
    
    // Test with the working COCO model
    const endpoint = `https://detect.roboflow.com/coco/3?api_key=${API_KEY}`;
    console.log(`🌐 Testing endpoint: ${endpoint}`);
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: base64Image
    });
    
    console.log(`📥 Response: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Success! Full Response:', JSON.stringify(result, null, 2));
      
      if (result.predictions && result.predictions.length > 0) {
        console.log('\n🎯 Detected Objects:');
        result.predictions.forEach((pred, index) => {
          console.log(`  ${index + 1}. ${pred.class} (confidence: ${(pred.confidence * 100).toFixed(1)}%)`);
          console.log(`     Position: x=${pred.x}, y=${pred.y}, w=${pred.width}, h=${pred.height}`);
        });
      } else {
        console.log('📝 No objects detected in this image');
      }
      
      // Now test our server endpoint
      console.log('\n🔄 Testing server integration...');
      await testServerIntegration(imageBuffer);
      
    } else {
      const errorText = await response.text();
      console.log(`❌ Error: ${errorText}`);
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

async function testServerIntegration(imageBuffer) {
  try {
    const FormData = (await import('form-data')).default;
    const formData = new FormData();
    formData.append('image', imageBuffer, {
      filename: 'test.jpg',
      contentType: 'image/jpeg'
    });
    
    console.log('📤 Sending to server...');
    const response = await fetch('http://localhost:3003/api/analyze-waste', {
      method: 'POST',
      body: formData
    });
    
    console.log(`📥 Server response: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Server success!');
      console.log(`🔍 API Source: ${result.apiSource}`);
      console.log(`📊 Total Points: ${result.totalPoints}`);
      console.log(`⚖️ Total Weight: ${result.totalWeight}kg`);
      
      if (result.wasteTypes && result.wasteTypes.length > 0) {
        console.log('\n🗂️ Detected Waste Types:');
        result.wasteTypes.forEach((waste, index) => {
          console.log(`  ${index + 1}. ${waste.type} - ${waste.quantity} items (${waste.points} points)`);
        });
      }
      
      if (result.error) {
        console.log(`⚠️ Error details: ${result.error}`);
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ Server error: ${errorText}`);
    }
    
  } catch (error) {
    console.error('💥 Server test failed:', error.message);
  }
}

testWithRealImage();
