// Clean imports for Gemini API integration
import fetch from 'node-fetch';

// Gemini API configuration
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || 'AIzaSyAZ8CypRH45o3jG6-Kk73s-ftWkzyYU_Ts';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

const wasteTypes = {
  'Plastic': { points: 100, minWeight: 0.1, maxWeight: 5 },
  'Glass': { points: 150, minWeight: 0.2, maxWeight: 3 },
  'Metal': { points: 120, minWeight: 0.1, maxWeight: 2 },
  'Paper': { points: 80, minWeight: 0.05, maxWeight: 2 },
  'Organic': { points: 60, minWeight: 0.1, maxWeight: 3 }
};

export async function analyzeWasteImage(imageBuffer) {
  console.log('🔍 Using Gemini API for waste analysis');

  try {
    return await analyzeWithGemini(imageBuffer);
  } catch (error) {
    console.error('❌ Gemini API error:', error);

    // Return detailed error information instead of fallback
    return {
      wasteTypes: [],
      totalPoints: 0,
      totalWeight: 0,
      apiSource: 'Gemini-Error',
      error: error.message,
      note: `Failed to analyze image with Gemini API: ${error.message}`,
      errorDetails: {
        type: error.type || 'unknown',
        code: error.code || 'unknown',
        errno: error.errno || 'unknown'
      }
    };
  }
}

// Gemini 1.5 Flash API integration
async function analyzeWithGemini(imageBuffer) {
  console.log('🤖 Analyzing with Gemini 1.5 Flash API...');

  try {
    // Convert image buffer to base64
    const base64Image = imageBuffer.toString('base64');

    // Prepare detailed prompt for specific waste identification
    const requestBody = {
      contents: [{
        parts: [
          {
            text: `Analyze this waste/recycling image and provide detailed identification. Return a JSON response with this exact structure:

{
  "wasteTypes": [
    {
      "type": "Glass|Plastic|Paper|Metal|Organic|Electronic|Hazardous",
      "specificType": "specific item name (e.g., 'Wine Bottle', 'Plastic Water Bottle', 'Cardboard Box')",
      "confidence": 0.95,
      "quantity": 1,
      "weight": 0.5,
      "points": 10
    }
  ],
  "totalPoints": 10,
  "totalWeight": 0.5,
  "apiSource": "Gemini"
}

Guidelines:
- Identify ALL visible waste items in the image
- Use specific item names (e.g., "Wine Bottle" not just "bottle")
- Weight estimation: Small items (0.1-0.3kg), Medium (0.3-0.8kg), Large (0.8-2.0kg)
- Points calculation: Glass=15, Metal=12, Paper=8, Plastic=5, Electronic=20, Organic=3, Hazardous=25
- Confidence should reflect actual certainty of identification
- If multiple items of same type, adjust quantity accordingly`
          },
          {
            inline_data: {
              mime_type: "image/jpeg",
              data: base64Image
            }
          }
        ]
      }],
      generationConfig: {
        temperature: 0.1,
        topK: 32,
        topP: 1,
        maxOutputTokens: 1024,
        responseMimeType: "application/json"
      }
    };

    console.log('📡 Making request to Gemini API...');

    // Add timeout and better error handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API HTTP ${response.status}:`, errorText);
      throw new Error(`Gemini API HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Received Gemini API response');

    // Extract the generated text from Gemini response
    if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts) {
      const generatedText = data.candidates[0].content.parts[0].text;

      try {
        // Parse the JSON response from Gemini
        const analysisResult = JSON.parse(generatedText);

        // Ensure the response has the correct structure
        if (analysisResult.wasteTypes && Array.isArray(analysisResult.wasteTypes)) {
          return {
            ...analysisResult,
            apiSource: 'Gemini'
          };
        }

        throw new Error('Invalid JSON structure in Gemini response');
      } catch (parseError) {
        console.error('Failed to parse Gemini JSON response:', parseError);
        console.error('Raw response text:', generatedText);
        throw new Error(`Failed to parse Gemini response: ${parseError.message}`);
      }
    } else {
      console.error('Invalid response structure:', data);
      throw new Error('Invalid response structure from Gemini API');
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Gemini API request timed out after 30 seconds');
    }
    console.error('Gemini API request failed:', error);
    throw error;
  }
}




