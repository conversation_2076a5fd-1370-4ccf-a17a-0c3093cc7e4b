.history-container {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.history-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.history-table th, 
.history-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.history-table th {
  font-weight: 600;
  color: #2e7d32;
}

.points {
  color: #2e7d32;
  font-weight: bold;
}

.submission-history {
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin: 2rem 0;
}

.submission-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

.stat-item {
  background: #e8f5e9;
  padding: 1.5rem;
  border-radius: 8px;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-item h3 {
  color: #2e7d32;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.stat-item p {
  color: #1b5e20;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: #333;
}

.filter-group select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.sort-order {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.sort-order:hover {
  background: #f1f3f5;
}

.submissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.submission-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.submission-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.submission-image {
  height: 200px;
  overflow: hidden;
}

.submission-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.submission-details {
  padding: 1.5rem;
}

.submission-details h4 {
  margin: 0 0 1rem 0;
  color: #2e7d32;
  font-size: 1.2rem;
}

.submission-details p {
  margin: 0.5rem 0;
  color: #666;
}

.submission-date {
  font-size: 0.9rem;
  color: #888 !important;
}

.submission-points {
  font-weight: 600;
  color: #2e7d32 !important;
}

.submission-description {
  margin-top: 1rem !important;
  padding-top: 1rem;
  border-top: 1px solid #eee;
  font-style: italic;
}

.no-submissions {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .submission-history {
    padding: 1rem;
    margin: 1rem 0;
  }

  .filters {
    flex-direction: column;
  }

  .filter-group {
    width: 100%;
  }

  .filter-group select {
    flex: 1;
  }

  .submissions-grid {
    grid-template-columns: 1fr;
  }
}