import fetch from 'node-fetch';

async function testServer() {
  console.log('🔍 Testing if server is running...');
  
  try {
    // Simple health check
    const response = await fetch('http://localhost:3003/api/analyze-waste', {
      method: 'GET'
    });
    
    console.log(`📥 Health check response: ${response.status} ${response.statusText}`);
    
    if (response.status === 404) {
      console.log('✅ Server is running (404 is expected for GET request)');
    } else {
      const text = await response.text();
      console.log(`📝 Response: ${text}`);
    }
    
  } catch (error) {
    console.error('❌ Server connection failed:', error.message);
    console.log('💡 Make sure the server is running with: node server/index.js');
  }
}

testServer();
