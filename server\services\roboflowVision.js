// Roboflow Vision Service - Free Object Detection
import fetch from 'node-fetch';

const ROBOFLOW_API_KEY = process.env.ROBOFLOW_API_KEY || 'your_free_api_key_here';
const MODEL_ENDPOINT = 'https://detect.roboflow.com/waste-classification/1';

export async function analyzeWasteImageRoboflow(imageBuffer) {
  try {
    console.log('Using Roboflow Vision API (Free)');
    
    // Convert buffer to base64
    const base64Image = imageBuffer.toString('base64');
    
    const response = await fetch(`${MODEL_ENDPOINT}?api_key=${ROBOFLOW_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: base64Image
    });

    if (!response.ok) {
      throw new Error(`Roboflow API error: ${response.statusText}`);
    }

    const results = await response.json();
    return processRoboflowResults(results);
    
  } catch (error) {
    console.error('Roboflow API error:', error);
    return await mockVisionAnalysis(imageBuffer);
  }
}

function processRoboflowResults(results) {
  const predictions = results.predictions || [];
  
  if (predictions.length === 0) {
    return generateFallbackResult();
  }

  // Group predictions by class
  const wasteTypes = {};
  
  predictions.forEach(pred => {
    const wasteType = mapRoboflowClass(pred.class);
    if (!wasteTypes[wasteType]) {
      wasteTypes[wasteType] = {
        count: 0,
        totalConfidence: 0,
        predictions: []
      };
    }
    wasteTypes[wasteType].count++;
    wasteTypes[wasteType].totalConfidence += pred.confidence;
    wasteTypes[wasteType].predictions.push(pred);
  });

  // Check if multiple types detected
  const typeKeys = Object.keys(wasteTypes);
  
  if (typeKeys.length > 1) {
    return generateMultipleTypesResult(wasteTypes);
  } else {
    return generateSingleTypeResult(typeKeys[0], wasteTypes[typeKeys[0]]);
  }
}

function mapRoboflowClass(className) {
  const classMap = {
    'plastic_bottle': 'Plastic',
    'plastic_container': 'Plastic',
    'plastic_bag': 'Plastic',
    'glass_bottle': 'Glass',
    'glass_jar': 'Glass',
    'metal_can': 'Metal',
    'aluminum_can': 'Metal',
    'paper': 'Paper',
    'cardboard': 'Paper',
    'organic': 'Organic'
  };
  
  return classMap[className.toLowerCase()] || 'Plastic';
}

function generateSingleTypeResult(wasteType, typeData) {
  const avgConfidence = typeData.totalConfidence / typeData.count;
  const weight = estimateWeight(wasteType, typeData.count);
  const points = calculateWastePoints(wasteType, weight, typeData.count);

  return {
    type: wasteType,
    weight: parseFloat(weight.toFixed(1)),
    quantity: typeData.count,
    points: points,
    confidence: parseFloat((avgConfidence * 100).toFixed(1)),
    detectedLabels: typeData.predictions.map(p => p.class),
    apiSource: 'Roboflow'
  };
}

function generateMultipleTypesResult(wasteTypes) {
  const results = [];
  let totalWeight = 0;
  let totalQuantity = 0;
  let totalPoints = 0;

  Object.entries(wasteTypes).forEach(([wasteType, typeData]) => {
    const avgConfidence = typeData.totalConfidence / typeData.count;
    const weight = estimateWeight(wasteType, typeData.count);
    const points = calculateWastePoints(wasteType, weight, typeData.count);

    results.push({
      type: wasteType,
      weight: parseFloat(weight.toFixed(1)),
      quantity: typeData.count,
      points: points,
      confidence: parseFloat((avgConfidence * 100).toFixed(1))
    });

    totalWeight += weight;
    totalQuantity += typeData.count;
    totalPoints += points;
  });

  return {
    multipleTypes: true,
    wasteTypes: results,
    summary: {
      totalTypes: results.length,
      totalWeight: parseFloat(totalWeight.toFixed(1)),
      totalQuantity: totalQuantity,
      totalPoints: totalPoints
    },
    apiSource: 'Roboflow'
  };
}

function estimateWeight(wasteType, quantity) {
  const weightMap = {
    'Plastic': 0.3,
    'Glass': 0.5,
    'Metal': 0.2,
    'Paper': 0.1,
    'Organic': 0.4
  };
  
  const baseWeight = weightMap[wasteType] || 0.3;
  return baseWeight * quantity * (0.8 + Math.random() * 0.4);
}

function calculateWastePoints(type, weight, quantity) {
  const pointsMap = {
    'Plastic': 100,
    'Glass': 150,
    'Metal': 120, 
    'Paper': 80,
    'Organic': 60
  };
  
  const basePoints = pointsMap[type] || 100;
  return Math.floor(basePoints * weight * quantity);
}

function generateFallbackResult() {
  return {
    type: 'Plastic',
    weight: 0.5,
    quantity: 1,
    points: 50,
    confidence: 60.0,
    detectedLabels: ['container'],
    apiSource: 'Roboflow-Fallback'
  };
}
