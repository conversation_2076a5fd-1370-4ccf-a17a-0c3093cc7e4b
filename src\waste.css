.upload-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.upload-container h2 {
  text-align: center;
  color: #2e7d32;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  position: relative;
}

.upload-container h2::after {
  content: '';
  display: block;
  width: 60px;
  height: 4px;
  background: #4CAF50;
  margin: 0.5rem auto;
  border-radius: 2px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input[type="file"] {
  width: 100%;
  padding: 0.75rem;
  border: 2px dashed #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.form-group input[type="file"]:hover {
  border-color: #4CAF50;
}

.image-preview {
  margin-top: 1rem;
  max-width: 300px;
}

.image-preview img {
  width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.submit-btn {
  background-color: #4CAF50;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: background-color 0.2s;
}

.submit-btn:hover {
  background-color: #45a049;
}

.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Analysis Status Styles */
.analysis-status {
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 1rem 0;
}

.spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto 1rem;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Analysis Results Styles */
.analysis-result {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analysis-result h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.result-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item span {
  font-weight: 600;
  color: #666;
}

.result-item.points {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 2px solid #4CAF50;
  font-size: 1.2rem;
  color: #4CAF50;
}

.result-item.points span {
  color: #2c3e50;
}

select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

.points-preview {
  margin: 1rem 0;
  padding: 1rem;
  background-color: #e8f5e9;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
  color: #2e7d32;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

textarea {
  resize: vertical;
  min-height: 100px;
}

/* Multiple Waste Types Styles */
.multiple-types-result {
  margin-top: 1rem;
}

.summary-section {
  background-color: #e8f5e9;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.summary-section h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #2e7d32;
}

.detailed-breakdown {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 6px;
}

.detailed-breakdown h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.waste-type-breakdown {
  background-color: white;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  border-left: 4px solid #4CAF50;
}

.waste-type-breakdown:last-child {
  margin-bottom: 0;
}

.waste-type-breakdown h5 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.breakdown-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.breakdown-details .result-item {
  padding: 0.25rem 0;
  border-bottom: 1px solid #eee;
  font-size: 0.9rem;
}

/* Detected Labels Styles */
.detected-labels {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #ddd;
}

.detected-labels h4 {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 1rem;
}

.labels-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.label-tag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Confidence and Evidence Indicators */
.result-item.fallback-notice {
  background-color: #fff3e0;
  color: #f57c00;
  padding: 0.5rem;
  border-radius: 4px;
  font-style: italic;
}

.result-item.fallback-notice span {
  color: #e65100;
  font-weight: bold;
}

/* Single Type Result Enhancements */
.single-type-result .result-item {
  padding: 0.75rem 0;
}

/* Responsive adjustments for multiple types */
@media (max-width: 768px) {
  .breakdown-details {
    grid-template-columns: 1fr;
  }

  .labels-list {
    justify-content: center;
  }

  .waste-type-breakdown {
    padding: 0.75rem;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .upload-container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .form-group input[type="text"],
  .form-group input[type="number"],
  .form-group select,
  .form-group textarea {
    font-size: 16px; /* Prevents zoom on mobile */
  }
}