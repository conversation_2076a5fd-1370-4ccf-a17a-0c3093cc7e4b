import fetch from 'node-fetch';

const API_KEY = 'NZOo17UfbOhl7BPh9ogJ';

async function testCorrectFormat() {
  console.log('🔍 Testing Roboflow API with correct format...');
  
  try {
    // Based on Roboflow documentation, the correct endpoint format is:
    // https://detect.roboflow.com/PROJECT_ID/VERSION?api_key=API_KEY
    
    // From your API response, your project is: v1/projects with version 2
    const correctEndpoint = `https://detect.roboflow.com/v1-projects/2?api_key=${API_KEY}`;
    console.log(`🌐 Testing correct endpoint: ${correctEndpoint}`);
    
    // Create a proper test image URL or use a sample image
    // For testing, let's use a simple approach with form data
    
    const testImageUrl = 'https://via.placeholder.com/300x200/0000FF/FFFFFF?text=Test+Image';
    console.log(`📷 Using test image URL: ${testImageUrl}`);
    
    // Method 1: Try with image URL
    try {
      const urlResponse = await fetch(correctEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: testImageUrl
        })
      });
      
      console.log(`📥 URL Method Response: ${urlResponse.status} ${urlResponse.statusText}`);
      
      if (urlResponse.ok) {
        const result = await urlResponse.json();
        console.log(`✅ URL Method Success:`, JSON.stringify(result, null, 2));
        return;
      } else {
        const error = await urlResponse.text();
        console.log(`❌ URL Method Error: ${error}`);
      }
    } catch (urlError) {
      console.log(`❌ URL Method Exception: ${urlError.message}`);
    }
    
    // Method 2: Try with base64 in JSON format
    console.log('\n🔄 Trying base64 in JSON format...');
    
    // Create a minimal valid base64 image (1x1 pixel PNG)
    const minimalPNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    
    const jsonResponse = await fetch(correctEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image: `data:image/png;base64,${minimalPNG}`
      })
    });
    
    console.log(`📥 JSON Method Response: ${jsonResponse.status} ${jsonResponse.statusText}`);
    
    if (jsonResponse.ok) {
      const result = await jsonResponse.json();
      console.log(`✅ JSON Method Success:`, JSON.stringify(result, null, 2));
    } else {
      const error = await jsonResponse.text();
      console.log(`❌ JSON Method Error: ${error}`);
    }
    
    // Method 3: Try the inference API format
    console.log('\n🔄 Trying inference API format...');
    
    const inferenceEndpoint = `https://detect.roboflow.com/v1-projects/2?api_key=${API_KEY}`;
    
    const formData = new URLSearchParams();
    formData.append('image', minimalPNG);
    
    const formResponse = await fetch(inferenceEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData
    });
    
    console.log(`📥 Form Method Response: ${formResponse.status} ${formResponse.statusText}`);
    
    if (formResponse.ok) {
      const result = await formResponse.json();
      console.log(`✅ Form Method Success:`, JSON.stringify(result, null, 2));
    } else {
      const error = await formResponse.text();
      console.log(`❌ Form Method Error: ${error}`);
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

testCorrectFormat();
