import fetch from 'node-fetch';
import fs from 'fs';

const API_KEY = 'NZOo17UfbOhl7BPh9ogJ';

async function testWithRealImage() {
  console.log('🔍 Testing Roboflow API with real image...');
  
  try {
    // Create a simple 100x100 PNG image with some content
    const createSimplePNG = () => {
      // This creates a minimal valid PNG
      const width = 100;
      const height = 100;
      
      // PNG signature
      const signature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
      
      // IHDR chunk
      const ihdrData = Buffer.alloc(13);
      ihdrData.writeUInt32BE(width, 0);
      ihdrData.writeUInt32BE(height, 4);
      ihdrData[8] = 8; // bit depth
      ihdrData[9] = 2; // color type (RGB)
      ihdrData[10] = 0; // compression
      ihdrData[11] = 0; // filter
      ihdrData[12] = 0; // interlace
      
      const ihdrCrc = 0x7D8DB4FB; // Pre-calculated CRC for this IHDR
      const ihdr = Buffer.concat([
        Buffer.from([0x00, 0x00, 0x00, 0x0D]), // length
        Buffer.from('IHDR'),
        ihdrData,
        Buffer.alloc(4)
      ]);
      ihdr.writeUInt32BE(ihdrCrc, ihdr.length - 4);
      
      // Simple IDAT chunk with minimal data
      const idatData = Buffer.from([
        0x78, 0x9C, 0xED, 0xC1, 0x01, 0x01, 0x00, 0x00, 0x00, 0x80, 0x90, 0xFE,
        0x37, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01
      ]);
      
      const idatCrc = 0x5E48F4C2; // Pre-calculated CRC
      const idat = Buffer.concat([
        Buffer.from([0x00, 0x00, 0x00, idatData.length]),
        Buffer.from('IDAT'),
        idatData,
        Buffer.alloc(4)
      ]);
      idat.writeUInt32BE(idatCrc, idat.length - 4);
      
      // IEND chunk
      const iend = Buffer.from([0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82]);
      
      return Buffer.concat([signature, ihdr, idat, iend]);
    };
    
    const imageBuffer = createSimplePNG();
    console.log(`📷 Created PNG image (${imageBuffer.length} bytes)`);
    
    // Test your custom project endpoint
    const endpoint = `https://detect.roboflow.com/v1/projects/2?api_key=${API_KEY}`;
    console.log(`🌐 Testing endpoint: ${endpoint}`);
    
    const base64Image = imageBuffer.toString('base64');
    console.log(`📤 Sending base64 image (${base64Image.length} chars)`);
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: base64Image
    });
    
    console.log(`📥 Response: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ Error: ${errorText}`);
      
      // Try the public COCO model
      console.log('\n🔄 Trying public COCO model...');
      const cocoEndpoint = `https://detect.roboflow.com/coco/3?api_key=${API_KEY}`;
      
      const cocoResponse = await fetch(cocoEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: base64Image
      });
      
      console.log(`📥 COCO Response: ${cocoResponse.status} ${cocoResponse.statusText}`);
      
      if (cocoResponse.ok) {
        const cocoResult = await cocoResponse.json();
        console.log(`✅ COCO Success:`, JSON.stringify(cocoResult, null, 2));
      } else {
        const cocoError = await cocoResponse.text();
        console.log(`❌ COCO Error: ${cocoError}`);
      }
      
      return;
    }
    
    const result = await response.json();
    console.log(`✅ Success:`, JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

testWithRealImage();
