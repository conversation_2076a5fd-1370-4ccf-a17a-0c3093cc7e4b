:root {
  --primary: #2e7d32;
  --primary-dark: #1b5e20;
  --primary-light: #e8f5e9;
  --text-dark: #2d3748;
  --text-light: #f8f9fa;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.home-page {
  padding: 2rem 0;
  max-width: 1400px;
  margin: 0 auto;
}

.hero {
  text-align: center;
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--primary-light) 0%, #ffffff 100%);
  border-radius: 16px;
  margin-bottom: 4rem;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(46, 125, 50, 0.1) 0%, rgba(255,255,255,0) 70%);
  animation: pulse 15s infinite linear;
}

@keyframes pulse {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.hero h1 {
  font-size: 3.5rem;
  color: var(--primary-dark);
  margin-bottom: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  position: relative;
  animation: fadeInUp 0.8s ease-out;
}

.hero p {
  font-size: 1.4rem;
  margin-bottom: 3rem;
  color: var(--text-dark);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.cta-button {
  padding: 1rem 2.5rem;
  font-size: 1.2rem;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
  box-shadow: 0 4px 6px rgba(46, 125, 50, 0.3);
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.cta-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(46, 125, 50, 0.4);
}

.how-it-works {
  text-align: center;
  margin: 5rem 0;
}

.how-it-works h2 {
  font-size: 2.5rem;
  color: var(--primary-dark);
  margin-bottom: 3rem;
  position: relative;
  display: inline-block;
}

.how-it-works h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--primary);
  border-radius: 2px;
}

.steps {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.step {
  flex: 1;
  padding: 2rem;
  min-width: 280px;
  max-width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: var(--shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.step::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background-color: var(--primary);
}

.step:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.step h3 {
  color: var(--primary);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.step p {
  color: var(--text-dark);
  font-size: 1.1rem;
  line-height: 1.6;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding: 3rem 1.5rem;
  }
  
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .hero p {
    font-size: 1.2rem;
  }
  
  .steps {
    flex-direction: column;
    align-items: center;
  }
  
  .step {
    max-width: 100%;
  }
}