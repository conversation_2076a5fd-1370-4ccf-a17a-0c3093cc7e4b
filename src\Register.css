/* src/auth.css - Professional Registration Page Styles */

:root {
  /* Color Palette */
  --primary: #2e7d32;
  --primary-dark: #1b5e20;
  --primary-light: #e8f5e9;
  --error: #d32f2f;
  --success: #388e3c;
  --text-primary: #212121;
  --text-secondary: #757575;
  --divider: #e0e0e0;
  --background: #f5f5f5;
  --surface: #ffffff;
  --shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-2: 0 4px 6px rgba(0,0,0,0.16), 0 4px 6px rgba(0,0,0,0.23);
  --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Layout */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  background-color: var(--background);
  background-image: linear-gradient(
    to bottom right,
    var(--primary-light),
    var(--background)
  );
}

.auth-card {
  width: 100%;
  max-width: 480px;
  padding: 3rem;
  background: var(--surface);
  border-radius: 16px;
  box-shadow: var(--shadow-2);
  overflow: hidden;
  position: relative;
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: var(--primary);
}

/* Typography */
.auth-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0.5rem;
  text-align: center;
}

.auth-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

/* Form Elements */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 1px solid var(--divider);
  border-radius: 8px;
  font-size: 1rem;
  transition: var(--transition);
  background-color: rgba(255, 255, 255, 0.9);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
}

/* Error States */
.form-input.error {
  border-color: var(--error);
  background-color: rgba(211, 47, 47, 0.05);
}

.error-message {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--error);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Password Strength Indicator */
.password-strength {
  height: 4px;
  background: var(--divider);
  border-radius: 2px;
  margin-top: 0.5rem;
  overflow: hidden;
}

.strength-meter {
  height: 100%;
  width: 0%;
  background: var(--error);
  transition: width 0.3s ease;
}

.strength-meter.medium {
  background: #ff9800;
}

.strength-meter.strong {
  background: var(--success);
}

/* Terms Checkbox */
.form-terms {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin: 1rem 0;
}

.form-terms input {
  margin-top: 0.25rem;
  accent-color: var(--primary);
}

.form-terms label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.form-terms a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
}

.form-terms a:hover {
  text-decoration: underline;
}

/* Submit Button */
.auth-btn {
  width: 100%;
  padding: 1rem;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.auth-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-1);
}

.auth-btn:disabled {
  background-color: #bdbdbd;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Spinner Animation */
.spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Footer Links */
.auth-footer {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.auth-link {
  color: var(--primary);
  font-weight: 500;
  text-decoration: none;
  margin-left: 0.25rem;
}

.auth-link:hover {
  text-decoration: underline;
}

/* Server Error Message */
.auth-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  background-color: rgba(211, 47, 47, 0.1);
  color: var(--error);
  border-radius: 8px;
  font-size: 0.875rem;
  animation: fadeIn 0.3s ease-out;
}

.auth-error svg {
  flex-shrink: 0;
  width: 1rem;
  height: 1rem;
}

/* Responsive Design */
@media (max-width: 600px) {
  .auth-container {
    padding: 1rem;
    align-items: flex-start;
  }
  
  .auth-card {
    padding: 2rem 1.5rem;
    margin-top: 2rem;
  }
  
  .auth-title {
    font-size: 1.5rem;
  }
}