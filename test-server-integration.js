import fetch from 'node-fetch';
import FormData from 'form-data';

async function testServerIntegration() {
  console.log('🔍 Testing server integration with Roboflow...');
  
  try {
    // Create a simple test image (bottle image URL that should be detected)
    const imageUrl = 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=300&fit=crop'; // Bottle image
    console.log(`📷 Using bottle image: ${imageUrl}`);
    
    // Download the image
    console.log('📥 Downloading image...');
    const imageResponse = await fetch(imageUrl);
    const imageBuffer = await imageResponse.buffer();
    console.log(`📤 Image downloaded (${imageBuffer.length} bytes)`);
    
    // Create form data
    const formData = new FormData();
    formData.append('image', imageBuffer, {
      filename: 'bottle.jpg',
      contentType: 'image/jpeg'
    });
    
    console.log('📤 Sending to server...');
    const response = await fetch('http://localhost:3003/api/analyze-waste', {
      method: 'POST',
      body: formData
    });
    
    console.log(`📥 Server response: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Server success!');
      console.log('📊 Full Response:', JSON.stringify(result, null, 2));
      
      // Check the API source
      if (result.apiSource) {
        if (result.apiSource.includes('Roboflow') && !result.apiSource.includes('Fallback')) {
          console.log('🎯 SUCCESS: Using actual Roboflow API!');
        } else if (result.apiSource.includes('Fallback')) {
          console.log('⚠️ WARNING: Using fallback instead of Roboflow API');
          if (result.error) {
            console.log(`🔍 Error details: ${result.error}`);
          }
        }
      }
      
      // Check detected waste types
      if (result.wasteTypes && result.wasteTypes.length > 0) {
        console.log('\n🗂️ Detected Waste Types:');
        result.wasteTypes.forEach((waste, index) => {
          console.log(`  ${index + 1}. ${waste.type} - ${waste.quantity} items`);
          console.log(`     Confidence: ${waste.confidence}%`);
          console.log(`     Points: ${waste.points}`);
          console.log(`     Weight: ${waste.weight}kg`);
        });
      }
      
      console.log(`\n📊 Summary:`);
      console.log(`   Total Points: ${result.totalPoints}`);
      console.log(`   Total Weight: ${result.totalWeight}kg`);
      console.log(`   API Source: ${result.apiSource}`);
      
    } else {
      const errorText = await response.text();
      console.log(`❌ Server error: ${errorText}`);
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testServerIntegration();
