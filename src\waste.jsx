// UploadWaste.jsx
import { useState } from 'react';
import { usePoints } from './PointsContext';
import './waste.css';

const wasteTypes = [
  { type: 'Plastic', points: 100, description: 'Plastic bottles, containers, packaging', minWeight: 0.1, maxWeight: 5 },
  { type: 'Glass', points: 150, description: 'Glass bottles, jars, containers', minWeight: 0.2, maxWeight: 3 },
  { type: 'Metal', points: 120, description: 'Metal cans, aluminum, steel containers', minWeight: 0.1, maxWeight: 2 },
  { type: 'Paper', points: 80, description: 'Paper, cardboard, newspapers', minWeight: 0.05, maxWeight: 2 },
  { type: 'Organic', points: 60, description: 'Food waste, organic materials', minWeight: 0.1, maxWeight: 3 }
];

export default function UploadWaste() {
  const { addSubmission } = usePoints();
  const [image, setImage] = useState(null);
  const [location, setLocation] = useState('');
  const [imagePreview, setImagePreview] = useState(null);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Simulate AI analysis of waste image
  const analyzeWasteImage = async () => {
    setIsAnalyzing(true);
    
    try {
      const formData = new FormData();
      formData.append('image', image);
      
      const response = await fetch('http://localhost:3003/api/analyze-waste', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error('Analysis failed');
      }
      
      const result = await response.json();
      setAnalysisResult(result);
    } catch (error) {
      console.error('Analysis failed:', error);
      // Fallback to mock analysis if API fails
      setTimeout(() => {
        const randomTypeIndex = Math.floor(Math.random() * wasteTypes.length);
        const selectedType = wasteTypes[randomTypeIndex];
        const randomWeight = (
          Math.random() * (selectedType.maxWeight - selectedType.minWeight) +
          selectedType.minWeight
        ).toFixed(1);

        setAnalysisResult({
          type: selectedType.type,
          weight: parseFloat(randomWeight),
          quantity: 1,
          points: selectedType.points,
          confidence: 75.0,
          fallback: true,
          detectedLabels: ['fallback analysis']
        });
        setIsAnalyzing(false);
      }, 2000);
      return;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
        // Start analysis when image is loaded - pass the file directly
        analyzeWasteImageWithFile(file);
      };
      reader.readAsDataURL(file);
    }
  };

  // New function that takes the file directly
  const analyzeWasteImageWithFile = async (file) => {
    setIsAnalyzing(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('http://localhost:3003/api/analyze-waste', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Analysis failed');
      }

      const result = await response.json();
      console.log('📊 Analysis result received:', result);
      setAnalysisResult(result);
    } catch (error) {
      console.error('Analysis failed:', error);
      // Fallback to mock analysis if API fails
      setTimeout(() => {
        const randomTypeIndex = Math.floor(Math.random() * wasteTypes.length);
        const selectedType = wasteTypes[randomTypeIndex];
        const randomWeight = (
          Math.random() * (selectedType.maxWeight - selectedType.minWeight) +
          selectedType.minWeight
        ).toFixed(1);

        setAnalysisResult({
          type: selectedType.type,
          weight: parseFloat(randomWeight),
          quantity: 1,
          points: selectedType.points,
          confidence: 75.0,
          fallback: true,
          detectedLabels: ['fallback analysis']
        });
        setIsAnalyzing(false);
      }, 2000);
      return;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!image || !location || !analysisResult) {
      alert('Please fill in all required fields and wait for analysis to complete');
      return;
    }

    // Handle multiple waste types or single type
    if (analysisResult.multipleTypes) {
      // Create separate submissions for each waste type
      analysisResult.wasteTypes.forEach((wasteType, index) => {
        const newSubmission = {
          id: Date.now() + index, // Ensure unique IDs
          date: new Date().toISOString().slice(0, 10),
          type: wasteType.type,
          points: wasteType.points,
          location,
          imageUrl: imagePreview,
          weight: wasteType.weight,
          quantity: wasteType.quantity,
          confidence: wasteType.confidence,
          multipleTypesSubmission: true,
          originalAnalysisId: Date.now() // Link related submissions
        };
        addSubmission(newSubmission);
      });
    } else {
      // Single waste type submission
      const newSubmission = {
        id: Date.now(),
        date: new Date().toISOString().slice(0, 10),
        type: analysisResult.type,
        points: analysisResult.points,
        location,
        imageUrl: imagePreview,
        weight: analysisResult.weight,
        quantity: analysisResult.quantity,
        confidence: analysisResult.confidence,
        textEvidence: analysisResult.textEvidence,
        fallback: analysisResult.fallback
      };
      addSubmission(newSubmission);
    }

    // Reset form
    setImage(null);
    setImagePreview(null);
    setLocation('');
    setAnalysisResult(null);
    e.target.reset();

    // Show success message
    alert(analysisResult.multipleTypes
      ? `Successfully submitted ${analysisResult.wasteTypes.length} waste types!`
      : 'Waste submission successful!'
    );
  };

  return (
    <div className="upload-container">
      <h2>Upload Your Waste</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>Waste Image</label>
          <input 
            type="file" 
            accept="image/*"
            onChange={handleImageChange}
            required
          />
          {imagePreview && (
            <div className="image-preview">
              <img src={imagePreview} alt="Waste preview" />
            </div>
          )}
        </div>

        {isAnalyzing ? (
          <div className="analysis-status">
            <div className="spinner"></div>
            <p>Analyzing waste image...</p>
          </div>
        ) : analysisResult && (
          <div className="analysis-result">
            <h3>Analysis Results</h3>

            {analysisResult.multipleTypes ? (
              <div className="multiple-types-result">
                <div className="summary-section">
                  <h4>Summary</h4>
                  <div className="result-item">
                    <span>Total Types Detected:</span> {analysisResult.summary.totalTypes}
                  </div>
                  <div className="result-item">
                    <span>Total Weight:</span> {analysisResult.summary.totalWeight} kg
                  </div>
                  <div className="result-item">
                    <span>Total Quantity:</span> {analysisResult.summary.totalQuantity} items
                  </div>
                  <div className="result-item points">
                    <span>Total Points Earned:</span> {analysisResult.summary.totalPoints}
                  </div>
                </div>

                <div className="detailed-breakdown">
                  <h4>Breakdown by Type</h4>
                  {analysisResult.wasteTypes.map((wasteType, index) => (
                    <div key={index} className="waste-type-breakdown">
                      <h5>{wasteType.specificType || wasteType.type}</h5>
                      {wasteType.specificType && wasteType.specificType !== wasteType.type && (
                        <div className="result-item specific-type">
                          <span>Category:</span> {wasteType.type}
                        </div>
                      )}
                      <div className="breakdown-details">
                        <div className="result-item">
                          <span>Weight:</span> {wasteType.weight} kg
                        </div>
                        <div className="result-item">
                          <span>Quantity:</span> {wasteType.quantity} items
                        </div>
                        <div className="result-item">
                          <span>Points:</span> {wasteType.points}
                        </div>
                        <div className="result-item">
                          <span>Confidence:</span> {wasteType.confidence}%
                        </div>
                        {wasteType.estimatedSize && (
                          <div className="result-item">
                            <span>Size:</span> {wasteType.estimatedSize}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="single-type-result">
                <div className="result-item">
                  <span>Type:</span> {analysisResult.specificType || analysisResult.type}
                </div>
                {analysisResult.specificType && analysisResult.specificType !== analysisResult.type && (
                  <div className="result-item">
                    <span>Category:</span> {analysisResult.type}
                  </div>
                )}
                <div className="result-item">
                  <span>Weight:</span> {analysisResult.weight} kg
                </div>
                <div className="result-item">
                  <span>Quantity:</span> {analysisResult.quantity} items
                </div>
                <div className="result-item">
                  <span>Confidence:</span> {analysisResult.confidence}%
                </div>
                {analysisResult.estimatedSize && (
                  <div className="result-item">
                    <span>Size:</span> {analysisResult.estimatedSize}
                  </div>
                )}
                <div className="result-item points">
                  <span>Points Earned:</span> {analysisResult.points}
                </div>
                {analysisResult.textEvidence && (
                  <div className="result-item">
                    <span>Material Evidence:</span> Text-based identification
                  </div>
                )}
                {(analysisResult.fallback || analysisResult.apiSource === 'Local-Intelligence') && (
                  <div className="result-item fallback-notice">
                    <span>Note:</span> {analysisResult.note || 'Fallback classification used'}
                  </div>
                )}
                {analysisResult.apiSource && (
                  <div className="result-item api-source">
                    <span>Analysis Source:</span> {analysisResult.apiSource}
                  </div>
                )}
              </div>
            )}

            {analysisResult.detectedLabels && analysisResult.detectedLabels.length > 0 && (
              <div className="detected-labels">
                <h4>Detected Labels</h4>
                <div className="labels-list">
                  {analysisResult.detectedLabels.slice(0, 5).map((label, index) => (
                    <span key={index} className="label-tag">{label}</span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        <div className="form-group">
          <label>Location</label>
          <input 
            type="text" 
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="Enter waste location"
            required
          />
        </div>

        <button 
          type="submit" 
          className="submit-btn"
          disabled={isAnalyzing || !analysisResult}
        >
          {isAnalyzing ? 'Analyzing...' : 'Submit Waste'}
        </button>
      </form>
    </div>
  );
}
