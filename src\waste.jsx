// UploadWaste.jsx
import { useState } from 'react';
import { usePoints } from './PointsContext';
import './waste.css';

const wasteTypes = [
  { type: 'Plastic', points: 100, description: 'Plastic bottles, containers, packaging', minWeight: 0.1, maxWeight: 5 },
  { type: 'Glass', points: 150, description: 'Glass bottles, jars, containers', minWeight: 0.2, maxWeight: 3 },
  { type: 'Metal', points: 120, description: 'Metal cans, aluminum, steel containers', minWeight: 0.1, maxWeight: 2 },
  { type: 'Paper', points: 80, description: 'Paper, cardboard, newspapers', minWeight: 0.05, maxWeight: 2 },
  { type: 'Organic', points: 60, description: 'Food waste, organic materials', minWeight: 0.1, maxWeight: 3 }
];

export default function UploadWaste() {
  const { addSubmission } = usePoints();
  const [image, setImage] = useState(null);
  const [location, setLocation] = useState('');
  const [imagePreview, setImagePreview] = useState(null);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);



  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
        // Start analysis when image is loaded - pass the file directly
        analyzeWasteImageWithFile(file);
      };
      reader.readAsDataURL(file);
    }
  };

  // New function that takes the file directly
  const analyzeWasteImageWithFile = async (file) => {
    setIsAnalyzing(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('http://localhost:3003/api/analyze-waste', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Analysis failed');
      }

      const result = await response.json();
      console.log('📊 Analysis result received:', result);
      setAnalysisResult(result);
    } catch (error) {
      console.error('Analysis failed:', error);
      // Fallback to mock analysis if API fails
      setTimeout(() => {
        const randomTypeIndex = Math.floor(Math.random() * wasteTypes.length);
        const selectedType = wasteTypes[randomTypeIndex];
        const randomWeight = (
          Math.random() * (selectedType.maxWeight - selectedType.minWeight) +
          selectedType.minWeight
        ).toFixed(1);

        setAnalysisResult({
          type: selectedType.type,
          weight: parseFloat(randomWeight),
          quantity: 1,
          points: selectedType.points,
          confidence: 75.0,
          fallback: true,
          detectedLabels: ['fallback analysis']
        });
        setIsAnalyzing(false);
      }, 2000);
      return;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!image || !location || !analysisResult) {
      alert('Please fill in all required fields and wait for analysis to complete');
      return;
    }

    // Handle waste types from Gemini API response
    if (analysisResult.wasteTypes && analysisResult.wasteTypes.length > 0) {
      // Create separate submissions for each waste type
      analysisResult.wasteTypes.forEach((wasteType, index) => {
        const newSubmission = {
          id: Date.now() + index, // Ensure unique IDs
          date: new Date().toISOString().slice(0, 10),
          type: wasteType.specificType || wasteType.type,
          points: wasteType.points || 0,
          location,
          imageUrl: imagePreview,
          weight: wasteType.weight || 0,
          quantity: wasteType.quantity || 0,
          confidence: wasteType.confidence || 0,
          multipleTypesSubmission: analysisResult.wasteTypes.length > 1,
          originalAnalysisId: Date.now(), // Link related submissions
          apiSource: analysisResult.apiSource
        };
        addSubmission(newSubmission);
      });
    } else {
      // Handle error case or no waste detected
      alert('No waste detected in the image. Please try with a different image.');
      return;
    }

    // Reset form
    setImage(null);
    setImagePreview(null);
    setLocation('');
    setAnalysisResult(null);
    e.target.reset();

    // Show success message
    alert(analysisResult.wasteTypes.length > 1
      ? `Successfully submitted ${analysisResult.wasteTypes.length} waste types!`
      : 'Waste submission successful!'
    );
  };

  return (
    <div className="upload-container">
      <h2>Upload Your Waste</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>Waste Image</label>
          <input 
            type="file" 
            accept="image/*"
            onChange={handleImageChange}
            required
          />
          {imagePreview && (
            <div className="image-preview">
              <img src={imagePreview} alt="Waste preview" />
            </div>
          )}
        </div>

        {isAnalyzing ? (
          <div className="analysis-status">
            <div className="spinner"></div>
            <p>Analyzing waste image...</p>
          </div>
        ) : analysisResult && (
          <div className="analysis-result">
            <h3>Analysis Results</h3>

            {analysisResult.wasteTypes && analysisResult.wasteTypes.length > 1 ? (
              <div className="multiple-types-result">
                <div className="summary-section">
                  <h4>Summary</h4>
                  <div className="result-item">
                    <span>Total Types Detected:</span> {analysisResult.wasteTypes.length}
                  </div>
                  <div className="result-item">
                    <span>Total Weight:</span> {analysisResult.totalWeight} kg
                  </div>
                  <div className="result-item">
                    <span>Total Quantity:</span> {analysisResult.wasteTypes.reduce((sum, item) => sum + item.quantity, 0)} items
                  </div>
                  <div className="result-item points">
                    <span>Total Points Earned:</span> {analysisResult.totalPoints}
                  </div>
                </div>

                <div className="detailed-breakdown">
                  <h4>Breakdown by Type</h4>
                  {analysisResult.wasteTypes.map((wasteType, index) => (
                    <div key={index} className="waste-type-breakdown">
                      <h5>{wasteType.specificType || wasteType.type}</h5>
                      {wasteType.specificType && wasteType.specificType !== wasteType.type && (
                        <div className="result-item specific-type">
                          <span>Category:</span> {wasteType.type}
                        </div>
                      )}
                      <div className="breakdown-details">
                        <div className="result-item">
                          <span>Weight:</span> {wasteType.weight} kg
                        </div>
                        <div className="result-item">
                          <span>Quantity:</span> {wasteType.quantity} items
                        </div>
                        <div className="result-item">
                          <span>Points:</span> {wasteType.points}
                        </div>
                        <div className="result-item">
                          <span>Confidence:</span> {wasteType.confidence}%
                        </div>
                        {wasteType.estimatedSize && (
                          <div className="result-item">
                            <span>Size:</span> {wasteType.estimatedSize}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : analysisResult.wasteTypes && analysisResult.wasteTypes.length === 1 ? (
              <div className="single-type-result">
                <div className="result-item">
                  <span>Type:</span> {analysisResult.wasteTypes[0].specificType || analysisResult.wasteTypes[0].type}
                </div>
                {analysisResult.wasteTypes[0].specificType && analysisResult.wasteTypes[0].specificType !== analysisResult.wasteTypes[0].type && (
                  <div className="result-item">
                    <span>Category:</span> {analysisResult.wasteTypes[0].type}
                  </div>
                )}
                <div className="result-item">
                  <span>Weight:</span> {analysisResult.wasteTypes[0].weight} kg
                </div>
                <div className="result-item">
                  <span>Quantity:</span> {analysisResult.wasteTypes[0].quantity} items
                </div>
                <div className="result-item">
                  <span>Confidence:</span> {analysisResult.wasteTypes[0].confidence * 100}%
                </div>
                <div className="result-item points">
                  <span>Points Earned:</span> {analysisResult.wasteTypes[0].points}
                </div>
                {analysisResult.apiSource && (
                  <div className="result-item api-source">
                    <span>Analysis Source:</span> {analysisResult.apiSource}
                  </div>
                )}
              </div>
            ) : (
              <div className="error-result">
                <div className="result-item">
                  <span>Error:</span> {analysisResult.error || 'No waste detected'}
                </div>
                {analysisResult.note && (
                  <div className="result-item">
                    <span>Note:</span> {analysisResult.note}
                  </div>
                )}
                {analysisResult.apiSource && (
                  <div className="result-item api-source">
                    <span>Analysis Source:</span> {analysisResult.apiSource}
                  </div>
                )}
              </div>
            )}

            {analysisResult.detectedLabels && analysisResult.detectedLabels.length > 0 && (
              <div className="detected-labels">
                <h4>Detected Labels</h4>
                <div className="labels-list">
                  {analysisResult.detectedLabels.slice(0, 5).map((label, index) => (
                    <span key={index} className="label-tag">{label}</span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        <div className="form-group">
          <label>Location</label>
          <input 
            type="text" 
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="Enter waste location"
            required
          />
        </div>

        <button 
          type="submit" 
          className="submit-btn"
          disabled={isAnalyzing || !analysisResult}
        >
          {isAnalyzing ? 'Analyzing...' : 'Submit Waste'}
        </button>
      </form>
    </div>
  );
}
