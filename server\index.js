import express from 'express';
import cors from 'cors';
import multer from 'multer';
import { analyzeWasteImage } from './services/visionService.js';

const app = express();
const PORT = process.env.PORT || 3003;

// Using Gemini 1.5 Flash API for waste classification
process.env.VISION_API = 'gemini';
process.env.GEMINI_API_KEY = 'AIzaSyAZ8CypRH45o3jG6-Kk73s-ftWkzyYU_Ts';

// Middleware
app.use(cors());
app.use(express.json());

// Configure multer for file uploads
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

// Analyze waste endpoint
app.post('/api/analyze-waste', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image provided' });
    }

    const analysis = await analyzeWasteImage(req.file.buffer);
    res.json(analysis);
  } catch (error) {
    console.error('Analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze image' });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Waste Management Server running on port ${PORT}`);
  console.log(`📊 Using ${process.env.VISION_API} vision API`);
  console.log(`🔗 API endpoint: http://localhost:${PORT}/api/analyze-waste`);
});