import fetch from 'node-fetch';

const API_KEY = 'NZOo17UfbOhl7BPh9ogJ';

async function testFinalRoboflow() {
  console.log('🔍 Final Roboflow API Test...');
  
  try {
    // Create a proper minimal PNG image (1x1 pixel)
    const minimalPNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    
    // Test the correct endpoint format for your project
    const endpoint = `https://detect.roboflow.com/v1-projects/2?api_key=${API_KEY}`;
    console.log(`🌐 Testing endpoint: ${endpoint}`);
    console.log(`📤 Sending minimal PNG (${minimalPNG.length} chars)`);
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: minimalPNG
    });
    
    console.log(`📥 Response: ${response.status} ${response.statusText}`);
    console.log(`📋 Headers:`, Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Success! Response:`, JSON.stringify(result, null, 2));
    } else {
      const errorText = await response.text();
      console.log(`❌ Error: ${errorText}`);
      
      // The issue might be that your project is private or the endpoint format is wrong
      // Let's check if there are any public models we can use
      console.log('\n🔍 Checking available public models...');
      
      // Try some known public models
      const publicModels = [
        'microsoft-coco/3',
        'coco/3',
        'yolov5s-coco/1'
      ];
      
      for (const model of publicModels) {
        try {
          const publicEndpoint = `https://detect.roboflow.com/${model}?api_key=${API_KEY}`;
          console.log(`\n🌐 Trying public model: ${publicEndpoint}`);
          
          const publicResponse = await fetch(publicEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: minimalPNG
          });
          
          console.log(`📥 Public model response: ${publicResponse.status} ${publicResponse.statusText}`);
          
          if (publicResponse.ok) {
            const publicResult = await publicResponse.json();
            console.log(`✅ Public model success! Response:`, JSON.stringify(publicResult, null, 2));
            return { endpoint: publicEndpoint, result: publicResult };
          } else {
            const publicError = await publicResponse.text();
            console.log(`❌ Public model error: ${publicError}`);
          }
        } catch (publicError) {
          console.log(`❌ Public model exception: ${publicError.message}`);
        }
      }
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
  
  console.log('\n📝 Summary:');
  console.log('- Your API key is valid (confirmed earlier)');
  console.log('- Your custom project (v1-projects/2) returns 403 Forbidden');
  console.log('- This suggests the project is private or requires different permissions');
  console.log('- For waste management, we can use the enhanced mock system instead');
  console.log('- The mock system provides realistic waste detection results');
}

testFinalRoboflow();
