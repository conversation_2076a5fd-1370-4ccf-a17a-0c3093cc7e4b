import fetch from 'node-fetch';
import fs from 'fs';
import FormData from 'form-data';

async function testWasteAnalysisAPI() {
  console.log('🧪 Testing Waste Analysis API with Gemini integration...');
  
  try {
    // Create a simple test image (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0x8B, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    // Create form data
    const formData = new FormData();
    formData.append('image', testImageBuffer, {
      filename: 'test-waste.png',
      contentType: 'image/png'
    });

    console.log('📡 Sending test image to waste analysis API...');
    
    const response = await fetch('http://localhost:3003/api/analyze-waste', {
      method: 'POST',
      body: formData
    });

    console.log(`📊 Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ HTTP ${response.status}:`, errorText);
      return;
    }

    const result = await response.json();
    console.log('✅ Waste analysis result:');
    console.log(JSON.stringify(result, null, 2));
    
    // Check if it's using Gemini API
    if (result.apiSource === 'Gemini') {
      console.log('🎉 SUCCESS: Using Gemini API for analysis!');
    } else if (result.apiSource === 'Gemini-Error') {
      console.log('⚠️ Gemini API error occurred:', result.error);
      console.log('Error details:', result.errorDetails);
    } else {
      console.log('❌ Not using Gemini API. API Source:', result.apiSource);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  }
}

testWasteAnalysisAPI();
