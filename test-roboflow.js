import fetch from 'node-fetch';
import fs from 'fs';

const API_KEY = 'NZOo17UfbOhl7BPh9ogJ';

async function testRoboflowAPI() {
  console.log('🔍 Testing Roboflow API Integration...');
  console.log(`🔑 API Key: ${API_KEY}`);
  
  try {
    // Create a simple test image buffer (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0x8B, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    console.log('📷 Created test image buffer');
    
    // Test different Roboflow endpoints
    const endpoints = [
      `https://detect.roboflow.com/coco/3?api_key=${API_KEY}`,
      `https://detect.roboflow.com/microsoft-coco/3?api_key=${API_KEY}`,
      `https://detect.roboflow.com/yolov5s-coco/1?api_key=${API_KEY}`
    ];
    
    for (let i = 0; i < endpoints.length; i++) {
      const endpoint = endpoints[i];
      console.log(`\n🌐 Testing endpoint ${i + 1}: ${endpoint}`);
      
      try {
        const base64Image = testImageBuffer.toString('base64');
        console.log(`📤 Sending base64 image (${base64Image.length} chars)`);
        
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: base64Image
        });
        
        console.log(`📥 Response status: ${response.status} ${response.statusText}`);
        console.log(`📋 Response headers:`, Object.fromEntries(response.headers.entries()));
        
        if (!response.ok) {
          const errorText = await response.text();
          console.log(`❌ Error response: ${errorText}`);
          continue;
        }
        
        const result = await response.json();
        console.log(`✅ Success! Response:`, JSON.stringify(result, null, 2));
        
        // If we get here, this endpoint works
        console.log(`🎯 Working endpoint found: ${endpoint}`);
        return { endpoint, result };
        
      } catch (error) {
        console.log(`❌ Error with endpoint ${i + 1}:`, error.message);
      }
    }
    
    console.log('\n❌ All endpoints failed. Checking API key validity...');
    
    // Test API key validity with a simple GET request
    const keyTestUrl = `https://api.roboflow.com/v1/projects?api_key=${API_KEY}`;
    console.log(`🔍 Testing API key with: ${keyTestUrl}`);
    
    const keyResponse = await fetch(keyTestUrl);
    console.log(`🔑 API Key test status: ${keyResponse.status}`);
    
    if (keyResponse.ok) {
      const keyResult = await keyResponse.json();
      console.log(`✅ API Key is valid. Projects:`, keyResult);
    } else {
      const keyError = await keyResponse.text();
      console.log(`❌ API Key test failed:`, keyError);
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

testRoboflowAPI();
